module.exports = {
  apps: [
    {
      name: 'cron-task',
      script: 'dist/server.js',
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      // Логирование
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Автоперезапуск при сбоях
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Cron restart (опционально - перезапуск каждый день в 2:00)
      cron_restart: '0 2 * * *',
      
      // Мониторинг
      pmx: true,
      
      // Graceful shutdown
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Переменные окружения из файла
      env_file: '.env'
    }
  ]
};
