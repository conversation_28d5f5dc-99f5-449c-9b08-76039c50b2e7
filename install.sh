#!/bin/bash

echo "🚀 Установка Cron Task на Ubuntu"

# Обновляем систему
sudo apt update && sudo apt upgrade -y

# Устанавливаем Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Устанавливаем Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Устанавливаем PM2 глобально
sudo npm install -g pm2

# Настраиваем проект
cp .env.example .env
npm install
npm run build

echo "✅ Установка завершена!"
echo ""
echo "📝 Следующие шаги:"
echo "1. Отредактируйте .env файл: nano .env"
echo "2. Перелогиньтесь: exit"
echo "3. Запустите nginx: docker-compose up -d"
echo "4. Запустите приложение: pm2 start ecosystem.config.js"
echo "5. Сохраните PM2: pm2 save && pm2 startup"
