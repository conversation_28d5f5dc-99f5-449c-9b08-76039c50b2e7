# 🚀 Простая установка на Ubuntu сервере

## Что будет работать:
- **nginx** в Docker контейнере (порт 80)
- **Node.js** с PM2 на сервере (порт 3000)
- **MongoDB** в Docker контейнере (порт 27017)

## Установка:

```bash
# 1. Загрузите проект на сервер
git clone <your-repo> && cd cron-task

# 2. Запустите установку
./install.sh

# 3. Перелогиньтесь
exit
# Подключитесь заново

# 4. Отредактируйте конфиг
nano .env

# 5. Запустите nginx и MongoDB
docker-compose up -d

# 6. Запустите Node.js приложение
pm2 start ecosystem.config.js

# 7. Сохраните PM2 для автозапуска
pm2 save
pm2 startup
```

## Проверка:

```bash
# Проверить nginx
curl http://localhost/

# Проверить статические файлы
curl http://localhost/public/index.html

# Статус PM2
pm2 status

# Логи PM2
pm2 logs

# Статус Docker
docker-compose ps
```

## Управление:

```bash
# Перезапуск Node.js
pm2 restart cron-task

# Перезапуск nginx
docker-compose restart nginx

# Остановка всего
pm2 stop cron-task
docker-compose down
```

Готово! 🎉
