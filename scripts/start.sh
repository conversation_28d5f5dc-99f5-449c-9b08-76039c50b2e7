#!/bin/bash

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Запуск Cron Task приложения${NC}"

# Проверяем наличие .env файла
if [ ! -f .env ]; then
    echo -e "${YELLOW}⚠️  Файл .env не найден. Создаем из примера...${NC}"
    if [ -f .env.example ]; then
        cp .env.example .env
        echo -e "${GREEN}✅ Файл .env создан из .env.example${NC}"
        echo -e "${YELLOW}📝 Пожалуйста, отредактируйте .env файл перед продолжением${NC}"
    else
        echo -e "${RED}❌ Файл .env.example не найден${NC}"
        exit 1
    fi
fi

# Проверяем наличие Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker не установлен${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose не установлен${NC}"
    exit 1
fi

echo -e "${BLUE}🔨 Сборка и запуск контейнеров...${NC}"

# Останавливаем существующие контейнеры
docker-compose down

# Собираем и запускаем
docker-compose up --build -d

# Ждем запуска сервисов
echo -e "${YELLOW}⏳ Ожидание запуска сервисов...${NC}"
sleep 10

# Проверяем статус
echo -e "${BLUE}📊 Статус контейнеров:${NC}"
docker-compose ps

# Проверяем доступность приложения
echo -e "${BLUE}🔍 Проверка доступности...${NC}"

# Проверяем nginx
if curl -s http://localhost > /dev/null; then
    echo -e "${GREEN}✅ Nginx доступен на http://localhost${NC}"
else
    echo -e "${RED}❌ Nginx недоступен${NC}"
fi

# Проверяем API
if curl -s http://localhost/ | grep -q "ok"; then
    echo -e "${GREEN}✅ API доступен${NC}"
else
    echo -e "${RED}❌ API недоступен${NC}"
fi

echo -e "${GREEN}🎉 Приложение запущено!${NC}"
echo -e "${BLUE}📱 Доступные адреса:${NC}"
echo -e "   • Веб-интерфейс: ${GREEN}http://localhost/public/index.html${NC}"
echo -e "   • API: ${GREEN}http://localhost/${NC}"
echo -e "   • Статические файлы: ${GREEN}http://localhost/public/${NC}"

echo -e "${BLUE}📋 Полезные команды:${NC}"
echo -e "   • Просмотр логов: ${YELLOW}docker-compose logs -f${NC}"
echo -e "   • Остановка: ${YELLOW}docker-compose down${NC}"
echo -e "   • Перезапуск: ${YELLOW}docker-compose restart${NC}"
