#!/bin/bash

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🛑 Остановка Cron Task приложения${NC}"

# Останавливаем контейнеры
echo -e "${YELLOW}⏳ Остановка контейнеров...${NC}"
docker-compose down

# Опционально удаляем volumes (раскомментируйте если нужно)
# echo -e "${YELLOW}🗑️  Удаление volumes...${NC}"
# docker-compose down -v

echo -e "${GREEN}✅ Приложение остановлено${NC}"

# Показываем статус
echo -e "${BLUE}📊 Статус контейнеров:${NC}"
docker-compose ps

echo -e "${BLUE}💡 Дополнительные команды:${NC}"
echo -e "   • Полная очистка (включая volumes): ${YELLOW}docker-compose down -v${NC}"
echo -e "   • Удаление образов: ${YELLOW}docker-compose down --rmi all${NC}"
echo -e "   • Запуск заново: ${YELLOW}./scripts/start.sh${NC}"
