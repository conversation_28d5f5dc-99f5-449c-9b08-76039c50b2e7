#!/bin/bash

# Скрипт автоматической установки на Ubuntu 24.04
# Использование: ./scripts/install-ubuntu.sh

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Установка Cron Task на Ubuntu 24.04${NC}"

# Проверяем права sudo
if ! sudo -n true 2>/dev/null; then
    echo -e "${RED}❌ Требуются права sudo${NC}"
    exit 1
fi

# Проверяем версию Ubuntu
if ! grep -q "24.04" /etc/os-release; then
    echo -e "${YELLOW}⚠️  Скрипт предназначен для Ubuntu 24.04${NC}"
    read -p "Продолжить? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo -e "${BLUE}📦 Обновление системы...${NC}"
sudo apt update && sudo apt upgrade -y

echo -e "${BLUE}🐳 Установка Docker...${NC}"

# Удаляем старые версии
sudo apt remove -y docker docker-engine docker.io containerd runc 2>/dev/null || true

# Устанавливаем зависимости
sudo apt install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    git \
    make

# Добавляем GPG ключ Docker
sudo mkdir -m 0755 -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Добавляем репозиторий Docker
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Устанавливаем Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Добавляем пользователя в группу docker
sudo usermod -aG docker $USER

echo -e "${GREEN}✅ Docker установлен${NC}"

echo -e "${BLUE}🔧 Настройка проекта...${NC}"

# Создаем .env файл если его нет
if [ ! -f .env ]; then
    cp .env.example .env
    echo -e "${GREEN}✅ Создан .env файл${NC}"
    echo -e "${YELLOW}📝 Отредактируйте .env файл перед запуском${NC}"
fi

# Делаем скрипты исполняемыми
chmod +x scripts/*.sh

# Создаем директории
mkdir -p logs
mkdir -p backups

echo -e "${BLUE}🔥 Настройка Firewall...${NC}"
sudo ufw --force enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

echo -e "${GREEN}🎉 Установка завершена!${NC}"
echo -e "${BLUE}📋 Следующие шаги:${NC}"
echo -e "   1. Отредактируйте файл .env:"
echo -e "      ${YELLOW}nano .env${NC}"
echo -e "   2. Перелогиньтесь для применения группы docker:"
echo -e "      ${YELLOW}exit${NC} (затем подключитесь заново)"
echo -e "   3. Запустите приложение:"
echo -e "      ${YELLOW}make start${NC}"
echo -e "   4. Проверьте работу:"
echo -e "      ${YELLOW}curl http://localhost/${NC}"

echo -e "${BLUE}💡 Полезные команды:${NC}"
echo -e "   • ${YELLOW}make help${NC} - список всех команд"
echo -e "   • ${YELLOW}make logs${NC} - просмотр логов"
echo -e "   • ${YELLOW}make status${NC} - статус контейнеров"

echo -e "${BLUE}📖 Подробная документация: ${YELLOW}DEPLOYMENT.md${NC}"
