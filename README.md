# Cron Task с Nginx

Проект представляет собой Node.js приложение с cron задачами, которое работает за nginx прокси-сервером.

## Архитектура

- **Nginx** - обслуживает статические файлы из папки `public/` и проксирует API запросы на Node.js
- **Node.js** - Express приложение с cron задачами
- **MongoDB** - база данных для хранения данных

## Структура проекта

```
├── src/                    # Исходный код TypeScript
│   ├── server.ts          # Основной сервер
│   ├── config/            # Конфигурация
│   ├── db/                # Подключение к БД и схемы
│   ├── external/          # Внешние API
│   └── votes/             # Логика голосований
├── public/                # Статические файлы (обслуживаются nginx)
│   └── votePictures/      # Загруженные изображения
├── nginx.conf             # Конфигурация nginx
├── docker-compose.yml     # Docker Compose конфигурация
├── Dockerfile             # Docker образ для Node.js приложения
└── .env                   # Переменные окружения
```

## Настройка и запуск

### 1. Подготовка переменных окружения

Скопируйте файл с примером переменных окружения:

```bash
cp .env.example .env
```

Отредактируйте `.env` файл:

```env
PORT=3000
MONGODB_URI=******************************************************************
INITIATIVE_ID=your-initiative-id
NODE_ENV=production
```

### 2. Запуск с Docker Compose

```bash
# Сборка и запуск всех сервисов
docker-compose up --build

# Запуск в фоновом режиме
docker-compose up -d --build

# Просмотр логов
docker-compose logs -f

# Остановка
docker-compose down
```

### 3. Локальная разработка

Для разработки без Docker:

```bash
# Установка зависимостей
npm install

# Запуск в режиме разработки
npm run dev

# Сборка проекта
npm run build

# Запуск продакшн версии
npm start
```

## Доступ к приложению

После запуска приложение будет доступно по адресам:

- **Веб-интерфейс**: http://localhost (nginx)
- **API**: http://localhost/api (проксируется на Node.js)
- **Статические файлы**: http://localhost/public/ (обслуживаются nginx)

## API Endpoints

- `GET /` - Проверка состояния приложения
- `POST /run-cron` - Ручной запуск cron задачи

## Nginx конфигурация

Nginx настроен для:

- Обслуживания статических файлов из `/public/` с кэшированием на 1 год
- Проксирования всех остальных запросов на Node.js приложение (порт 3000)
- Gzip сжатия для оптимизации
- Правильной передачи заголовков для работы за прокси

## Мониторинг

### Просмотр логов

```bash
# Логи всех сервисов
docker-compose logs

# Логи конкретного сервиса
docker-compose logs nginx
docker-compose logs app
docker-compose logs mongo

# Следить за логами в реальном времени
docker-compose logs -f app
```

### Проверка состояния

```bash
# Проверка работы API
curl http://localhost/

# Проверка статических файлов
curl http://localhost/public/

# Проверка состояния контейнеров
docker-compose ps
```

## Разработка

### Добавление новых статических файлов

Поместите файлы в папку `public/`. Они будут автоматически доступны по адресу `http://localhost/public/filename`.

### Изменение конфигурации nginx

1. Отредактируйте `nginx.conf`
2. Перезапустите nginx контейнер:

```bash
docker-compose restart nginx
```

### Обновление Node.js приложения

1. Внесите изменения в код
2. Пересоберите и перезапустите:

```bash
docker-compose up --build app
```

## Производственное развертывание

Для продакшн среды рекомендуется:

1. Использовать внешнюю MongoDB
2. Настроить SSL/TLS сертификаты для nginx
3. Настроить логирование и мониторинг
4. Использовать Docker Swarm или Kubernetes для оркестрации

### SSL/TLS настройка

Добавьте в `nginx.conf`:

```nginx
server {
    listen 443 ssl;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    # ... остальная конфигурация
}
```

## Устранение неполадок

### Проблемы с подключением к MongoDB

Проверьте, что MongoDB контейнер запущен:

```bash
docker-compose ps mongo
docker-compose logs mongo
```

### Проблемы с nginx

Проверьте конфигурацию nginx:

```bash
docker-compose exec nginx nginx -t
docker-compose logs nginx
```

### Проблемы с Node.js приложением

```bash
docker-compose logs app
docker-compose exec app npm run build
```
