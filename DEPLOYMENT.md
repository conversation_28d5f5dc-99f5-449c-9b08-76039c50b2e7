# 🚀 Развертывание на Ubuntu 24.04 Server

Подробное руководство по развертыванию приложения Cron Task на Ubuntu 24.04 сервере.

## 📋 Предварительные требования

- Ubuntu 24.04 Server
- Доступ к серверу по SSH с правами sudo
- Домен или IP адрес сервера

## 🔧 Установка зависимостей

### 1. Обновление системы

```bash
sudo apt update && sudo apt upgrade -y
```

### 2. Установка Docker

```bash
# Удаляем старые версии Docker (если есть)
sudo apt remove docker docker-engine docker.io containerd runc

# Устанавливаем зависимости
sudo apt install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# Добавляем официальный GPG ключ Docker
sudo mkdir -m 0755 -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# Добавляем репозиторий Docker
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# Устанавливаем Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

# Добавляем пользователя в группу docker
sudo usermod -aG docker $USER

# Перелогиниваемся или выполняем
newgrp docker

# Проверяем установку
docker --version
docker compose version
```

### 3. Установка дополнительных инструментов

```bash
# Git для клонирования репозитория
sudo apt install -y git

# Make для удобных команд
sudo apt install -y make

# Nginx (если планируете использовать без Docker)
sudo apt install -y nginx

# Certbot для SSL сертификатов
sudo apt install -y certbot python3-certbot-nginx
```

## 📁 Развертывание приложения

### 1. Клонирование репозитория

```bash
# Переходим в домашнюю директорию
cd ~

# Клонируем репозиторий (замените на ваш URL)
git clone https://github.com/your-username/cron-task.git
cd cron-task

# Или загружаем файлы другим способом
```

### 2. Настройка переменных окружения

```bash
# Копируем пример конфигурации
cp .env.example .env

# Редактируем конфигурацию
nano .env
```

Пример содержимого `.env`:

```env
# Порт для Node.js приложения
PORT=3000

# MongoDB подключение
MONGODB_URI=***************************************************************************

# ID инициативы
INITIATIVE_ID=your-initiative-id

# Окружение
NODE_ENV=production
```

### 3. Настройка первоначальных разрешений

```bash
# Делаем скрипты исполняемыми
chmod +x scripts/*.sh

# Создаем директории для логов
mkdir -p logs

# Настраиваем права доступа
sudo chown -R $USER:$USER .
```

## 🐳 Запуск с Docker (Рекомендуется)

### 1. Запуск приложения

```bash
# Первоначальная настройка
make setup

# Запуск всех сервисов
make start

# Или альтернативно
docker compose up -d --build
```

### 2. Проверка работы

```bash
# Проверяем статус контейнеров
docker compose ps

# Проверяем логи
docker compose logs -f

# Тестируем API
curl http://localhost/

# Тестируем статические файлы
curl http://localhost/public/index.html
```

### 3. Управление сервисами

```bash
# Остановка
make stop

# Перезапуск
make restart

# Просмотр логов
make logs

# Полная очистка
make clean
```

## 🔧 Запуск без Docker (альтернативный способ)

### 1. Установка Node.js

```bash
# Устанавливаем Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Проверяем установку
node --version
npm --version
```

### 2. Установка MongoDB

```bash
# Импортируем публичный ключ
wget -qO - https://www.mongodb.org/static/pgp/server-7.0.asc | sudo apt-key add -

# Добавляем репозиторий
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-7.0.list

# Устанавливаем MongoDB
sudo apt update
sudo apt install -y mongodb-org

# Запускаем и включаем автозапуск
sudo systemctl start mongod
sudo systemctl enable mongod

# Создаем пользователя для базы данных
mongosh --eval "
use admin
db.createUser({
  user: 'admin',
  pwd: 'your_secure_password',
  roles: ['userAdminAnyDatabase', 'dbAdminAnyDatabase', 'readWriteAnyDatabase']
})
"
```

### 3. Установка и настройка приложения

```bash
# Устанавливаем зависимости
npm install

# Собираем проект
npm run build

# Устанавливаем PM2 глобально
sudo npm install -g pm2

# Запускаем приложение с PM2
npm run start:pm2

# Настраиваем автозапуск PM2
pm2 startup
pm2 save
```

### 4. Настройка Nginx

```bash
# Создаем конфигурацию для сайта
sudo nano /etc/nginx/sites-available/cron-task
```

Содержимое файла:

```nginx
server {
    listen 80;
    server_name your-domain.com;  # Замените на ваш домен

    # Статические файлы
    location /public/ {
        alias /home/<USER>/cron-task/public/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri $uri/ =404;
    }

    # API проксирование
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Активируем сайт
sudo ln -s /etc/nginx/sites-available/cron-task /etc/nginx/sites-enabled/

# Удаляем дефолтный сайт
sudo rm /etc/nginx/sites-enabled/default

# Проверяем конфигурацию
sudo nginx -t

# Перезапускаем Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

## 🔒 Настройка SSL сертификата

```bash
# Получаем SSL сертификат от Let's Encrypt
sudo certbot --nginx -d your-domain.com

# Настраиваем автообновление
sudo crontab -e
# Добавляем строку:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔥 Настройка Firewall

```bash
# Включаем UFW
sudo ufw enable

# Разрешаем SSH
sudo ufw allow ssh

# Разрешаем HTTP и HTTPS
sudo ufw allow 80
sudo ufw allow 443

# Проверяем статус
sudo ufw status
```

## 📊 Мониторинг и логи

### PM2 мониторинг

```bash
# Просмотр статуса процессов
pm2 status

# Просмотр логов
pm2 logs

# Мониторинг в реальном времени
pm2 monit

# Перезапуск приложения
pm2 restart cron-task
```

### Системные логи

```bash
# Логи Nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Логи MongoDB
sudo tail -f /var/log/mongodb/mongod.log

# Системные логи
sudo journalctl -u nginx -f
sudo journalctl -u mongod -f
```

## 🔄 Обновление приложения

```bash
# Переходим в директорию проекта
cd ~/cron-task

# Получаем обновления
git pull origin main

# Пересобираем (для Docker)
make restart

# Или для PM2
npm run build
pm2 restart cron-task
```

## 🆘 Устранение неполадок

### Проверка портов

```bash
# Проверяем какие порты слушаются
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :3000
```

### Проверка процессов

```bash
# Docker процессы
docker compose ps

# PM2 процессы
pm2 status

# Системные процессы
ps aux | grep node
ps aux | grep nginx
```

### Перезапуск сервисов

```bash
# Nginx
sudo systemctl restart nginx

# MongoDB
sudo systemctl restart mongod

# PM2 приложение
pm2 restart cron-task

# Docker сервисы
docker compose restart
```

## 📝 Полезные команды

```bash
# Просмотр использования ресурсов
htop
df -h
free -h

# Очистка Docker
docker system prune -a

# Бэкап базы данных
mongodump --authenticationDatabase admin -u admin -p your_password --out backup/

# Восстановление базы данных
mongorestore --authenticationDatabase admin -u admin -p your_password backup/
```

## 🎯 Готово!

После выполнения всех шагов ваше приложение будет доступно по адресу:
- **HTTP**: http://your-domain.com
- **HTTPS**: https://your-domain.com (если настроен SSL)

Приложение будет автоматически запускаться при перезагрузке сервера и перезапускаться при сбоях благодаря PM2.
