# ⚡ Быстрый запуск на Ubuntu 24.04

## 🚀 Автоматическая установка (Рекомендуется)

```bash
# 1. Клонируйте репозиторий
git clone <your-repo-url>
cd cron-task

# 2. Запустите автоматическую установку
./scripts/install-ubuntu.sh

# 3. Перелогиньтесь для применения группы docker
exit
# Подключитесь заново по SSH

# 4. Отредактируйте конфигурацию
nano .env

# 5. Запустите приложение
make start

# 6. Проверьте работу
curl http://localhost/
```

## 🐳 Запуск с Docker (Прод<PERSON><PERSON>шн)

```bash
# Настройка
make setup
nano .env

# Запуск
make start

# Управление
make logs     # Просмотр логов
make restart  # Перезапуск
make stop     # Остановка
```

## 🔧 Запуск с PM2 (Альтернатива)

```bash
# Установка Node.js и зависимостей
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
npm install

# Запуск с PM2
make pm2-start

# Управление
make pm2-logs    # Логи
make pm2-restart # Перезапуск
make pm2-stop    # Остановка
```

## 📊 Проверка работы

```bash
# API
curl http://localhost/

# Статические файлы
curl http://localhost/public/index.html

# Статус контейнеров
make status

# Тестирование
make test
```

## 🔒 Настройка домена и SSL

```bash
# Замените your-domain.com на ваш домен в nginx.conf
nano nginx.conf

# Получите SSL сертификат
sudo certbot --nginx -d your-domain.com
```

## 📋 Полезные команды

```bash
make help          # Все доступные команды
make logs          # Логи всех сервисов
make logs-app      # Логи только приложения
make backup-db     # Бэкап базы данных
make clean         # Полная очистка
```

## 🆘 Устранение проблем

```bash
# Проверка портов
sudo netstat -tlnp | grep :80

# Перезапуск nginx
sudo systemctl restart nginx

# Логи системы
sudo journalctl -f
```

## 📖 Подробная документация

Смотрите [DEPLOYMENT.md](DEPLOYMENT.md) для детального руководства.
