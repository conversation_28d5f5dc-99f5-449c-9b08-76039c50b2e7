import TelegramBot from "node-telegram-bot-api";
import {env} from "../config/env";
import {getCaptcha} from "../external";
import {recursiveLoadVotes} from "../votes";

export const bot = new TelegramBot(env.botToken, { polling: true });

const userCaptchaMap = new Map<number, string>();
const resolvedCaptchaMap = new Map<string, string>();

bot.on("message", async (msg) => {
  let image: string | undefined = undefined
  try{

    if (!msg.text) return;

    if(msg.text === '/start') {
      const captcha = await getCaptcha()
      image = captcha.image

      return bot.sendPhoto(msg.chat.id, Buffer.from(captcha.image, "base64"), {
        caption: "Iltimos, javobni yuboring!",
        reply_markup: {
          inline_keyboard: [
            [{ text: "Javob yuborish", callback_data: captcha.captchaKey }],
          ],
        },
      });
    }

    const userId = msg.from?.id;
    if (!userId) return;

    const captchaKey = userCaptchaMap.get(userId);
    if (!captchaKey) return;

    if(resolvedCaptchaMap.has(captchaKey)) return;

    const result = msg.text;

    const loaded = await recursiveLoadVotes({captcha: {captchaKey, result}})

    if(loaded){
      resolvedCaptchaMap.set(captchaKey, result)
    }

    await bot.sendMessage(msg.chat.id, "✅ Javob qabul qilindi!");

    userCaptchaMap.delete(userId);

  }catch (e) {
    console.log('Bot error::: ', e)
    console.log('image::: ', image)
  }
});

bot.on("callback_query", async (query) => {
 try {
   const captchaKey = query.data!;
   const userId = query.from.id;

   userCaptchaMap.set(userId, captchaKey);

   await bot.answerCallbackQuery(query.id, { text: "Endi javobni yuboring!" });
   await bot.sendMessage(env.tgChatId, `✏️ Javobni yuboring: ${query.from?.first_name || query.from?.username || ''}`);
 }catch (e) {
   console.log('callback query error ', e)
 }
});

export async function sendCaptcha () {
  try {
    const captcha = await getCaptcha()

    const buffer = Buffer.from(captcha.image, 'base64');

    await bot.sendPhoto(env.tgChatId, buffer, {
      caption: "Iltimos, javobni yuboring!",
      reply_markup: {
        inline_keyboard: [
          [{ text: "Javob yuborish", callback_data: captcha.captchaKey }],
        ],
      },
    });

    console.log('Rasm yuborildi!');
  }catch (e) {
    console.error('Xatolik:', e);
  }
}