import express from 'express';
import cron from 'node-cron';
import { config } from 'dotenv';
import {connectDB} from "./db/connect";
import * as mongoose from "mongoose";

config();

const app = express();
const PORT = process.env.PORT || 3000;

// Sog'liqni tekshirish
app.get("/", (_req, res) => {
    res.json({ ok: true, message: "Cron app ishlayapti" });
});

// 15 daqiqada bir marta bajariladigan cron
// Cron ifoda: '*/15 * * * *'  => har 15 daqiqa
const task = cron.schedule(
    "*/1 * * * *",
    () => {
        const now = new Date();
        console.log(`[CRON] ${now.toISOString()} - 15 daqiqalik ish bajarildi`);
    },
    {
        timezone: "Asia/Tashkent", // ixtiyoriy, sizning vaqt zonangiz
    }
);

// Qo'l bilan ishga tushirish uchun endpoint (debug uchun qulay)
app.post("/run-cron", (_req, res) => {
    const now = new Date();
    console.log(`[MANUAL] ${now.toISOString()} - Cron vazifasi qo'lda ishga tushirildi`);
    res.json({ ok: true });
});

// Serverni ishga tushirish
let server: ReturnType<typeof app.listen>;

connectDB().then(() => {
    server = app.listen(PORT, () => {
        console.log(`🚀 Server http://localhost:${PORT} da ishlayapti`);
        console.log("⏰ 15 minutlik cron job ishga tushdi.");
    });
});

// graceful shutdown
function shutdown(signal: string) {
    console.log(`\n${signal} qabul qilindi. To'xtatilmoqda...`);
    try {
        task.stop();
    } catch {}
    mongoose.connection.close(false)
    server.close(() => {
        console.log("HTTP server yopildi.");
        process.exit(0);
    });
    setTimeout(() => process.exit(0), 5000);
}

process.on("SIGINT", () => shutdown("SIGINT"));
process.on("SIGTERM", () => shutdown("SIGTERM"));