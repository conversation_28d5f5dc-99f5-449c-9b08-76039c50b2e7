import express from 'express';
import cron from 'node-cron';
import { config } from 'dotenv';
import {connectDB} from "./db/connect";
import * as mongoose from "mongoose";
import path from "path";
import cors from "cors";
import {bot, sendCaptcha} from "./bot";
import {externalRouter} from "./routes/external";

config();

const app = express();
const PORT = process.env.PORT || 3000;

// Настройка для работы за прокси (nginx)
app.set('trust proxy', true);

// Middleware для парсинга JSON
app.use(cors({ origin: "*" }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Отдача статических файлов из папки public
// Определяем правильный путь к папке public
const publicPath = process.env.NODE_ENV === 'production'
    ? path.join(process.cwd(), 'public')  // В продакшн папка public в корне проекта
    : path.join(__dirname, '../public');  // В разработке относительно src

console.log('📁 Public path:', publicPath);

// Настраиваем отдачу статических файлов по маршруту /public
app.use('/public', express.static(publicPath, {
    maxAge: '1y',        // Кэширование на 1 год
    etag: true,          // Включаем ETag
    lastModified: true,  // Включаем Last-Modified
    index: false         // Отключаем автоматический index.html
}));

app.use('/api/v1', externalRouter)

// Sog'liqni tekshirish
app.get("/", (_req, res) => {
    res.json({ ok: true, message: "Cron app ishlayapti" });
});

// 15 daqiqada bir marta bajariladigan cron
// Cron ifoda: '*/15 * * * *'  => har 15 daqiqa
const task = cron.schedule(
    "*/1 * * * *",
    async () => {
        const now = new Date();
        console.log(`[CRON] ${now.toISOString()} - 15 daqiqalik ish bajarildi`);
        await sendCaptcha()
    },
    {
        timezone: "Asia/Tashkent", // ixtiyoriy, sizning vaqt zonangiz
    }
);

// Qo'l bilan ishga tushirish uchun endpoint (debug uchun qulay)
app.post("/run-cron", (_req, res) => {
    const now = new Date();
    console.log(`[MANUAL] ${now.toISOString()} - Cron vazifasi qo'lda ishga tushirildi`);
    res.json({ ok: true });
});

// Serverni ishga tushirish
let server: ReturnType<typeof app.listen>;

connectDB().then(() => {
    server = app.listen(PORT, async () => {
        console.log(`🚀 Server http://localhost:${PORT} da ishlayapti`);
        console.log("⏰ 15 minutlik cron job ishga tushdi.");
    });
});

// graceful shutdown
function shutdown(signal: string) {
    console.log(`\n${signal} qabul qilindi. To'xtatilmoqda...`);
    try {
        task.stop();
    } catch {}
    mongoose.connection.close(false)
    server.close(() => {
        console.log("HTTP server yopildi.");
        process.exit(0);
    });
    void bot.stopPolling()
    setTimeout(() => process.exit(0), 5000);
}

process.on("SIGINT", () => shutdown("SIGINT"));
process.on("SIGTERM", () => shutdown("SIGTERM"));