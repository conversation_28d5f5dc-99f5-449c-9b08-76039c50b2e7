import {env} from "../config/env";
import {getExternalImageUrl, getInitiativeToken, getVotes} from "../external";
import path from "path";
import fs from "fs/promises";
import {Readable} from "node:stream";
import fsStream from "fs";
import {VotesSchema} from "../db/schema";

type Props = {
  captcha: {
    captchaKey: string;
    result: string
  }
}

async function downloadAndSaveFile(url: string, outputDir: string, fileName: string) {
  try {
    const filePath = path.join(outputDir, fileName);

    // Создаем папку, если ее нет
    await fs.mkdir(outputDir, { recursive: true });

    // Загружаем файл с помощью fetch
    const response = await fetch(url);
    if (!response.ok || !response.body) {
      throw new Error(`Не удалось загрузить ${url}: ${response.statusText}`);
    }

    // Преобразуем Web stream в Node.js stream
    const nodeStream = Readable.fromWeb(response.body as any);
    const writer = fsStream.createWriteStream(filePath);

    nodeStream.pipe(writer);

    // Ждем завершения записи
    await new Promise((resolve, reject) => {
      // @ts-expect-error
      writer.on('finish', resolve);
      writer.on('error', reject);
    });

    console.log(`Файл ${fileName} успешно загружен и сохранен`);
  } catch (error) {
    console.error(`Ошибка при загрузке ${url}:`, (error as Error)?.message);
  }
}

export async function downloadAllFiles (urls: string[]) {
  const output = path.join(process.cwd(), 'public', 'votePictures')
  const promises = urls.map(url => downloadAndSaveFile(getExternalImageUrl(url), output, `${url}.png`))
  await Promise.all(promises)
}

export async function recursiveLoadVotes ({captcha}: Props) {

  try {

    const body = {
      initiativeId: env.initiativeId,
      captchaKey: captcha.captchaKey,
      captchaResult: captcha.result
    }

    const token = await getInitiativeToken(body)

    const recursiveLoad = async (page: number) => {
      const ph = await getVotes({page: page.toString(), token: token.token})

      if(ph.content?.length){
        await downloadAllFiles(ph.content.map(i => i.image))
        const result = await VotesSchema.insertMany(ph.content.map(vote => ({imageKey: vote.image, voteDate: vote.voteDate})));
        console.log(`${result.length} documents were inserted`, result);
        await recursiveLoad(page + 1)
      }
    }

    await recursiveLoad(0)

    return true

  }catch {
    return false
  }

}