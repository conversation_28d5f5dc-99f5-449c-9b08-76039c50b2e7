import { config } from 'dotenv';

config()

const initiativeId = process.env.INITIATIVE_ID
const mongoDbUri = process.env.MONGODB_URI
const botToken = process.env.BOT_TOKEN
const tgChatId = process.env.TG_CHAT_ID

if(!initiativeId){
  throw new Error("INITIATIVE_ID is not defined")
}

if(!mongoDbUri){
  throw new Error("MONGODB_URI is not defined")
}

if(!botToken){
  throw new Error("BOT_TOKEN is not defined")
}

if(!tgChatId){
  throw new Error("TG_CHAT_ID is not defined")
}

export const env = {
  initiativeId,
  mongoDbUri,
  botToken,
  tgChatId
}
