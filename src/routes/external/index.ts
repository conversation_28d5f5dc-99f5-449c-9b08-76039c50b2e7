import {Router} from "express";
import {getCaptcha, getInitiativeToken, getVotes} from "../../external";

const externalRouter = Router()

externalRouter.get('/captcha', async (req, res) => {
  try {
    const captcha = await getCaptcha()
    res.json(captcha)
  }catch (e) {
    res.status(500).json({error: (e as Error).message})
  }
})

externalRouter.get('/initiative-token', async (req, res) => {
  try {
    const token = await getInitiativeToken(req.body)
    res.json(token)
  }catch (e) {
    res.status(500).json({error: (e as Error).message})
  }
})

externalRouter.get('/votes', async (req, res) => {
  try {
    const {token, ...query} = req.query
    const result = await getVotes({token: token as string, ...query})
    res.json(result)
  }catch (e) {
    res.status(500).json({error: (e as Error).message})
  }
})


export {externalRouter}