{"name": "cron-task", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "start:pm2": "pm2 start ecosystem.config.js", "stop:pm2": "pm2 stop cron-task", "restart:pm2": "pm2 restart cron-task", "reload:pm2": "pm2 reload cron-task", "delete:pm2": "pm2 delete cron-task", "logs:pm2": "pm2 logs cron-task", "monit:pm2": "pm2 monit", "dev": "ts-node-dev --respawn --transpile-only src/server.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "mongoose": "^8.17.1", "node-cron": "^4.2.1", "pm2": "^5.3.0", "query-string": "^9.2.2"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/mongoose": "^5.11.96", "@types/node": "^24.3.0", "ts-node-dev": "^2.0.0", "typescript": "^5.9.2"}}