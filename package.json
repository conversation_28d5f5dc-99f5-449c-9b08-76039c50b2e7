{"name": "cron-task", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"dotenv": "^17.2.1", "express": "^5.1.0", "mongoose": "^8.17.1", "node-cron": "^4.2.1", "query-string": "^9.2.2"}, "devDependencies": {"@types/express": "^5.0.3", "@types/mongoose": "^5.11.96", "@types/node": "^24.3.0", "ts-node-dev": "^2.0.0", "typescript": "^5.9.2"}}