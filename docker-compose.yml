version: '3.8'

services:
  # Nginx веб-сервер
  nginx:
    image: nginx:alpine
    container_name: cron-task-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./public:/usr/share/nginx/html/public:ro
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # MongoDB
  mongo:
    image: mongo:7
    container_name: cron-task-mongo
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
    volumes:
      - mongo_data:/data/db
    ports:
      - "27017:27017"

volumes:
  mongo_data:
