version: '3.8'

services:
  # Node.js приложение
  app:
    build: .
    container_name: cron-task-app
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3000
    volumes:
      - ./.env:/app/.env:ro
      - ./public:/app/public
    networks:
      - app-network
    depends_on:
      - mongo
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx веб-сервер
  nginx:
    image: nginx:alpine
    container_name: cron-task-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./public:/usr/share/nginx/html/public:ro
    networks:
      - app-network
    depends_on:
      - app

  # MongoDB (если используется)
  mongo:
    image: mongo:7
    container_name: cron-task-mongo
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
    volumes:
      - mongo_data:/data/db
    ports:
      - "27017:27017"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  mongo_data:
