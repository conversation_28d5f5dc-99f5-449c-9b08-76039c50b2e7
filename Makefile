.PHONY: help start stop restart logs build clean dev test

# Цвета для вывода
BLUE=\033[0;34m
GREEN=\033[0;32m
YELLOW=\033[1;33m
NC=\033[0m

help: ## Показать справку
	@echo "$(BLUE)Доступные команды:$(NC)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}'

start: ## Запустить приложение
	@echo "$(BLUE)🚀 Запуск приложения...$(NC)"
	@./scripts/start.sh

stop: ## Остановить приложение
	@echo "$(BLUE)🛑 Остановка приложения...$(NC)"
	@./scripts/stop.sh

restart: stop start ## Перезапустить приложение

logs: ## Показать логи
	@echo "$(BLUE)📋 Логи приложения:$(NC)"
	@docker-compose logs -f

logs-app: ## Показать логи Node.js приложения
	@echo "$(BLUE)📋 Логи Node.js:$(NC)"
	@docker-compose logs -f app

logs-nginx: ## Показать логи Nginx
	@echo "$(BLUE)📋 Логи Nginx:$(NC)"
	@docker-compose logs -f nginx

logs-mongo: ## Показать логи MongoDB
	@echo "$(BLUE)📋 Логи MongoDB:$(NC)"
	@docker-compose logs -f mongo

build: ## Пересобрать образы
	@echo "$(BLUE)🔨 Пересборка образов...$(NC)"
	@docker-compose build --no-cache

clean: ## Очистить все (контейнеры, образы, volumes)
	@echo "$(YELLOW)⚠️  Полная очистка...$(NC)"
	@docker-compose down -v --rmi all
	@docker system prune -f

dev: ## Запустить в режиме разработки (локально)
	@echo "$(BLUE)🔧 Режим разработки...$(NC)"
	@npm run dev

test: ## Проверить работу приложения
	@echo "$(BLUE)🧪 Тестирование...$(NC)"
	@curl -s http://localhost/ | grep -q "ok" && echo "$(GREEN)✅ API работает$(NC)" || echo "$(YELLOW)❌ API недоступен$(NC)"
	@curl -s http://localhost/public/index.html | grep -q "Cron Task" && echo "$(GREEN)✅ Статические файлы работают$(NC)" || echo "$(YELLOW)❌ Статические файлы недоступны$(NC)"

status: ## Показать статус контейнеров
	@echo "$(BLUE)📊 Статус контейнеров:$(NC)"
	@docker-compose ps

shell-app: ## Подключиться к контейнеру приложения
	@docker-compose exec app sh

shell-nginx: ## Подключиться к контейнеру nginx
	@docker-compose exec nginx sh

backup-db: ## Создать бэкап базы данных
	@echo "$(BLUE)💾 Создание бэкапа...$(NC)"
	@mkdir -p backups
	@docker-compose exec mongo mongodump --authenticationDatabase admin -u admin -p password123 --out /tmp/backup
	@docker cp cron-task-mongo:/tmp/backup ./backups/backup-$(shell date +%Y%m%d-%H%M%S)
	@echo "$(GREEN)✅ Бэкап создан в папке backups/$(NC)"

install: ## Установить зависимости
	@echo "$(BLUE)📦 Установка зависимостей...$(NC)"
	@npm install

setup: ## Первоначальная настройка проекта
	@echo "$(BLUE)⚙️  Настройка проекта...$(NC)"
	@if [ ! -f .env ]; then cp .env.example .env && echo "$(GREEN)✅ Создан .env файл$(NC)"; fi
	@chmod +x scripts/*.sh
	@echo "$(GREEN)✅ Проект настроен$(NC)"
	@echo "$(YELLOW)📝 Не забудьте отредактировать .env файл$(NC)"
