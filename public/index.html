<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cron Task - Статические файлы</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .api-test {
            background: #f0f8ff;
            border: 1px solid #2196f3;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f9f9f9;
            border: 1px solid #ddd;
        }
        .success { background: #e8f5e8; border-color: #4caf50; }
        .error { background: #ffe8e8; border-color: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Cron Task Application</h1>
        
        <div class="status">
            <h3>✅ Статические файлы работают!</h3>
            <p>Этот файл обслуживается nginx напрямую из папки <code>public/</code></p>
            <p><strong>URL:</strong> <code>/public/index.html</code></p>
        </div>

        <div class="api-test">
            <h3>🔗 Тестирование API</h3>
            <p>Проверьте работу проксирования запросов на Node.js приложение:</p>
            
            <button onclick="testHealthCheck()">Проверить состояние API</button>
            <button onclick="testManualCron()">Запустить cron вручную</button>
            
            <div id="api-result" class="result" style="display: none;"></div>
        </div>

        <div class="api-test">
            <h3>📁 Структура проекта</h3>
            <ul>
                <li><strong>Nginx:</strong> Обслуживает статические файлы и проксирует API</li>
                <li><strong>Node.js:</strong> Express приложение с cron задачами</li>
                <li><strong>MongoDB:</strong> База данных для хранения данных</li>
            </ul>
        </div>

        <div class="api-test">
            <h3>🔧 Полезные команды</h3>
            <pre><code># Просмотр логов
docker-compose logs -f

# Перезапуск сервисов
docker-compose restart

# Остановка
docker-compose down</code></pre>
        </div>
    </div>

    <script>
        async function testHealthCheck() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Проверяем...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('/');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <strong>✅ API работает!</strong><br>
                        Ответ: ${JSON.stringify(data, null, 2)}
                    `;
                    resultDiv.className = 'result success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <strong>❌ Ошибка API:</strong><br>
                    ${error.message}
                `;
                resultDiv.className = 'result error';
            }
        }

        async function testManualCron() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Запускаем cron...';
            resultDiv.className = 'result';

            try {
                const response = await fetch('/run-cron', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <strong>✅ Cron запущен!</strong><br>
                        Ответ: ${JSON.stringify(data, null, 2)}
                    `;
                    resultDiv.className = 'result success';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <strong>❌ Ошибка запуска cron:</strong><br>
                    ${error.message}
                `;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
